---
description: 
globs: 
alwaysApply: false
---
# Project Structure

This project is a React application built with React Router 7 and Ant Design.

## Main Entry Points
- [app/root.tsx](mdc:app/root.tsx) - Root component of the application
- [app/routes.ts](mdc:app/routes.ts) - Defines all routes for the application
- [vite.config.ts](mdc:vite.config.ts) - Vite configuration for the project

## Directory Structure

### Application Structure
- @app/components/layouts/MainLayout.tsx - The main layout component
- [app/routes/home.tsx](mdc:app/routes/home.tsx) - Home page component
- [app/pages/NotFound.tsx](mdc:app/pages/NotFound.tsx) - 404 page component

### Configuration
- [react-router.config.ts](mdc:react-router.config.ts) - React Router configuration
- [tsconfig.json](mdc:tsconfig.json) - TypeScript configuration 
- [.eslintrc.json](mdc:.eslintrc.json) - ESLint configuration
- [.prettierrc](mdc:.prettierrc) - Prettier configuration
