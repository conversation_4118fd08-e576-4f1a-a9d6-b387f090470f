---
description: 
globs: 
alwaysApply: false
---
# Coding Conventions

## General Guidelines
- Follow the user's requirements carefully & to the letter
- First think step-by-step - describe your plan for what to build in pseudocode, written out in great detail
- Confirm, then write code!
- Always write correct, best practice, DRY principle (Don't Repeat Yourself), bug free, fully functional and working code
- Focus on code readability and maintainability over performance optimization
- Fully implement all requested functionality
- Leave NO todos, placeholders or missing pieces
- Ensure code is complete! Verify thoroughly finalized
- Include all required imports, and ensure proper naming of key components
- Be concise and minimize any unnecessary prose
- If you think there might not be a correct answer, say so
- If you do not know the answer, say so, instead of guessing

## TypeScript & JavaScript
- Use TypeScript for all new code
- Enable strict mode in tsconfig.json
- Define explicit types for all variables, functions, and components
- Use interfaces for object types and type for primitive types
- Utilize type inference when types are obvious
- Use early returns whenever possible to make the code more readable
- Use const instead of let whenever possible
- Use arrow functions for component definitions and callbacks

## React Components
- Use functional components with hooks
- Follow the React hooks naming convention (use prefix)
- Implement proper error boundaries
- Use React.memo() for performance optimization when needed
- Implement proper prop types and default props
- Use destructuring for props
- Keep components small and focused on a single responsibility
- Follow the React Router 7 conventions for routing
- Use Ant Design components for UI consistency

## File Structure
- Place page components in `app/components/`
- Place reusable layouts in `app/components/layouts/`
- Place route components in `app/routes/`
- Use PascalCase for component file names (e.g., `MainLayout.tsx`)
- Use camelCase for utility files (e.g., `routes.ts`)


## Styling
- Always use Tailwind classes for styling HTML elements
- Avoid inline styles and CSS files
- Use ":" instead of the ternary operator in class tags whenever possible
- Follow mobile-first responsive design principles
- Use Tailwind's dark mode when implementing dark themes

## Naming Conventions
- Use descriptive variable and function/const names
- Event handlers should be named with "handle" prefix (e.g., handleClick, handleSubmit)
- Component names should be in PascalCase
- File names should match component names
- Use camelCase for variables and functions
- Use UPPER_SNAKE_CASE for constants

## Accessibility
- Implement proper ARIA labels and roles
- Ensure proper keyboard navigation (tabindex="0")
- Add proper alt text for images
- Use semantic HTML elements
- Ensure proper color contrast
- Implement proper focus management

## State Management
- Use React Context for global state when appropriate
- Implement proper loading and error states
- Use proper state initialization
- Implement proper cleanup in useEffect hooks

## Performance
- Implement proper code splitting
- Use React.lazy() for component lazy loading
- Optimize re-renders using useMemo and useCallback
- Implement proper image optimization
- Use proper caching strategies

## Testing
- Write unit tests for components
- Implement proper error handling
- Use proper testing utilities (React Testing Library)
- Follow testing best practices
