---
description: 
globs: 
alwaysApply: false
---
# Tech Stack

This project is built using the following technologies:

## Frontend
- ReactJS (with React 19+ features)
- React-Router (v7.5+)(@React Router)
- AntDesign (v5+)(@Ant Design)
  - AntDesign/Pro-Layout(@Ant Design Pro)
  - AntDesign/Pro-Component(@Ant Design Pro-Component)
- TypeScript (strict mode)
- TailwindCSS
- HTML5
- CSS3 (via Tailwind)

## Build Tools
- Vite
- TypeScript
- ESLint for code linting
- Prettier for code formatting

## Dev & Deploy
- Docker for containerization
- Bun for package management

## Package Management
- Use Bun as the primary package manager and runtime environment
- Initialize new projects with `bun init`
- Install dependencies using `bun install` or `bun add`
- Use `bun run` for running scripts defined in package.json
- Leverage Bun's built-in TypeScript support without additional configuration
- Take advantage of <PERSON><PERSON>'s faster package installation and execution speeds
- Use Bun's built-in test runner with `bun test`
- Utilize B<PERSON>'s native fetch API and other built-in APIs
- Use `bunx` for running one-off commands (equivalent to npx)
- Manage environment variables with Bun's built-in .env support

## Scripts
- `bun run dev` - Start the development server
- `bun run build` - Build the application for production
- `bun run start` - Start the production server
- `bun run typecheck` - Run type checking
- `bun run lint` - Run linting
- `bun run format` - Format code with Prettier
