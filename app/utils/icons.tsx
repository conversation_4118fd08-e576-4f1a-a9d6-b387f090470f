// utils/icons.tsx
import {
  HomeOutlined,
  DashboardOutlined,
  Area<PERSON>hartOutlined,
  MonitorOutlined,
  TeamOutlined,
  UserOutlined,
  EyeOutlined,
  PlusOutlined,
  SafetyOutlined,
  LockOutlined,
  SettingOutlined,
  ToolOutlined,
  MailOutlined,
  <PERSON>Outlined,
  <PERSON>Outlined,
  FileTextOutlined,
  ApiOutlined,
} from '@ant-design/icons';

const iconComponents = {
  HomeOutlined,
  DashboardOutlined,
  AreaChartOutlined,
  MonitorOutlined,
  TeamOutlined,
  UserOutlined,
  EyeOutlined,
  PlusOutlined,
  SafetyOutlined,
  LockOutlined,
  SettingOutlined,
  ToolOutlined,
  MailOutlined,
  BellOutlined,
  CloudOutlined,
  FileTextOutlined,
  ApiOutlined,
};

export type IconName = keyof typeof iconComponents;

export function getIconComponent(iconName: IconName): React.ComponentType {
  return iconComponents[iconName];
}
