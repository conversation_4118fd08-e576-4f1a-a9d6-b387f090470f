import api from '~/libs/api';

// Types
export interface MenuCategory {
  id: string;
  name: string;
  nameZh: string;
  description?: string;
  seq: number;
  valid: boolean;
}

export interface MenuItem {
  id: string;
  name: string;
  nameZh: string;
  path: string;
  icon: string;
  description?: string;
  parentId?: string;
  seq: number;
  valid: boolean;
}

export interface MenuCategoryItem {
  id: string;
  categoryId: string;
  itemId: string;
  order?: number;
  status?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// Menu Category Methods
export const getMenuCategories = () => {
  return api.get<MenuCategory[]>('/platform/menu-categories');
};

export const getMenuCategory = (id: string) => {
  return api.get<MenuCategory>(`/platform/menu-categories/${id}`);
};

export const createMenuCategory = (data: Partial<MenuCategory>) => {
  return api.post<MenuCategory>('/platform/menu-categories', data);
};

export const updateMenuCategory = (id: string, data: Partial<MenuCategory>) => {
  return api.put<MenuCategory>(`/platform/menu-categories/${id}`, data);
};

export const deleteMenuCategory = (id: string) => {
  return api.delete(`/platform/menu-categories/${id}`);
};

// Menu Item Methods
export const getMenuItems = () => {
  return api.get<MenuItem[]>('/platform/menu-items');
};

export const getMenuItem = (id: string) => {
  return api.get<MenuItem>(`/platform/menu-items/${id}`);
};

export const createMenuItem = (data: Partial<MenuItem>) => {
  return api.post<MenuItem>('/platform/menu-items', data);
};

export const updateMenuItem = (id: string, data: Partial<MenuItem>) => {
  return api.put<MenuItem>(`/platform/menu-items/${id}`, data);
};

export const deleteMenuItem = (id: string) => {
  return api.delete(`/platform/menu-items/${id}`);
};

// Menu Category Item Methods
export const getMenuCategoryItems = (categoryId: string) => {
  return api.get<MenuCategoryItem[]>(`/platform/menu-categories/${categoryId}/items`);
};

export const getMenuCategoryItem = (categoryId: string, itemId: string) => {
  return api.get<MenuCategoryItem>(`/platform/menu-categories/${categoryId}/items/${itemId}`);
};

export const addItemToCategory = (categoryId: string, itemId: string, data?: Partial<MenuCategoryItem>) => {
  return api.post<MenuCategoryItem>(`/platform/menu-categories/${categoryId}/items/${itemId}`, data);
};

export const updateMenuCategoryItem = (categoryId: string, itemId: string, data: Partial<MenuCategoryItem>) => {
  return api.put<MenuCategoryItem>(`/platform/menu-categories/${categoryId}/items/${itemId}`, data);
};

export const removeItemFromCategory = (categoryId: string, itemId: string) => {
  return api.delete(`/platform/menu-categories/${categoryId}/items/${itemId}`);
};
