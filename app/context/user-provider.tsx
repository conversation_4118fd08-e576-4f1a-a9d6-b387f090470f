import React, { createContext, useContext, useEffect, useState } from 'react';
import type { ReactNode } from 'react';
import type { UserInfo } from '~/service/auth.service';
import { getSavedUserInfo, fetchUserInfo, logout } from '~/service/auth.service';
import { useNavigate } from 'react-router';

interface UserContextType {
  user: UserInfo | null;
  isLoading: boolean;
  error: Error | null;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const UserContext = createContext<UserContextType>({
  user: null,
  isLoading: true,
  error: null,
  logout: async () => {},
  refreshUser: async () => {},
});

export const useUser = () => useContext(UserContext);

export function UserProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<UserInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const navigate = useNavigate();

  const loadUser = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // First check session storage
      const savedUser = getSavedUserInfo();
      if (savedUser) {
        setUser(savedUser);
        setIsLoading(false);
        return;
      }

      // If no saved user, fetch from API
      const userInfo = await fetchUserInfo();
      setUser(userInfo);
    } catch (err) {
      console.error('Error loading user:', err);
      setError(err instanceof Error ? err : new Error('Failed to load user'));

      // Redirect to login page when user info fetch fails
      window.location.href = 'http://localhost:8080';
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      setUser(null);
      // Redirect to login page after logout
      window.location.href = 'http://localhost:8080';
    } catch (err) {
      console.error('Error during logout:', err);
    }
  };

  // Load user on initial mount
  useEffect(() => {
    loadUser();
  }, []);

  return (
    <UserContext.Provider
      value={{
        user,
        isLoading,
        error,
        logout: handleLogout,
        refreshUser: loadUser,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}
