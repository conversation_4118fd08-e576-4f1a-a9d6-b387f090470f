import React, { useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import SettingContext from './setting-context';
import { defaultLang } from '~/config/lang';
import { defaultTheme } from '~/config/theme';
import type { LayoutType, NavTheme } from '~/config/theme';

interface SettingProviderProps {
  children: ReactNode;
}

// Check if we're running in the browser environment
const isBrowser = typeof window !== 'undefined';

export const SettingProvider: React.FC<SettingProviderProps> = ({ children }) => {
  // Get initial theme from sessionStorage or use default
  const [theme, setThemeState] = useState(() => {
    try {
      if (isBrowser) {
        const savedTheme = sessionStorage.getItem('theme-preference');
        if (savedTheme) {
          return { ...defaultTheme, ...JSON.parse(savedTheme) };
        }
      }
    } catch (error) {
      console.error('Error reading theme from sessionStorage:', error);
    }
    return defaultTheme;
  });

  const [lang, setLang] = useState(defaultLang);

  // Wrapper for setTheme that also saves to sessionStorage
  const setTheme = (newTheme: typeof theme) => {
    setThemeState(newTheme);
    try {
      if (isBrowser) {
        sessionStorage.setItem(
          'theme-preference',
          JSON.stringify({
            navTheme: newTheme.navTheme,
            colorPrimary: newTheme.colorPrimary,
          })
        );
      }
    } catch (error) {
      console.error('Error saving theme to sessionStorage:', error);
    }
  };

  return <SettingContext.Provider value={{ theme, setTheme, lang, setLang }}>{children}</SettingContext.Provider>;
};

export const useSettings = () => {
  return React.useContext(SettingContext);
};
