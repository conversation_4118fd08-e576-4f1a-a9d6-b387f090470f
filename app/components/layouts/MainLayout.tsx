import {
  AlertOutlined,
  <PERSON><PERSON><PERSON>utlined,
  <PERSON><PERSON><PERSON>Outlined,
  AreaChartOutlined,
  AuditOutlined,
  BankOutlined,
  Bar<PERSON>hart<PERSON>utlined,
  BellOutlined,
  BookOutlined,
  BranchesOutlined,
  BugOutlined,
  BuildOutlined,
  CalendarOutlined,
  CarryOutOutlined,
  CheckCircleOutlined,
  CloseOutlined,
  ClearOutlined,
  ClockCircleOutlined,
  CloudOutlined,
  CodeOutlined,
  CommentOutlined,
  ContactsOutlined,
  CustomerServiceOutlined,
  DashboardOutlined,
  DatabaseOutlined,
  DeleteOutlined,
  DesktopOutlined,
  DollarOutlined,
  DownloadOutlined,
  EditOutlined,
  EyeOutlined,
  FieldTimeOutlined,
  FileOutlined,
  FileSearchOutlined,
  FileTextOutlined,
  FolderOutlined,
  FormOutlined,
  FundOutlined,
  GlobalOutlined,
  HddOutlined,
  HistoryOutlined,
  HomeOutlined,
  IdcardOutlined,
  ImportOutlined,
  InboxOutlined,
  InfoCircleOutlined,
  KeyOutlined,
  LaptopOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON>Outlined,
  LogoutOutlined,
  <PERSON>Outlined,
  <PERSON>uOutlined,
  MonitorOutlined,
  <PERSON>Outlined,
  <PERSON>de<PERSON>ndexOutlined,
  Not<PERSON><PERSON>utlined,
  OrderedListOutlined,
  PhoneOutlined,
  PictureOutlined,
  <PERSON><PERSON>hartOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
  SafetyCertificateOutlined,
  SafetyOutlined,
  SaveOutlined,
  SearchOutlined,
  SecurityScanOutlined,
  SettingOutlined,
  SolutionOutlined,
  SunOutlined,
  SwapOutlined,
  TeamOutlined,
  ThunderboltOutlined,
  ToolOutlined,
  TranslationOutlined,
  UploadOutlined,
  UserOutlined,
  UsergroupAddOutlined,
} from '@ant-design/icons';
import { ProLayout, SettingDrawer } from '@ant-design/pro-layout';
import { Avatar, ConfigProvider, Dropdown, Space, App as AntdApp, theme, Menu, Tabs } from 'antd';
import React, { useState, useEffect } from 'react';
import { Link, Outlet, useLocation, useNavigate } from 'react-router';
import { useTranslation } from 'react-i18next';
import EnglishIcon from '~/assets/icons/english.svg?react';

import MenuPanelDropdown from './MenuPanelDropdown';
import { useUser } from '~/context/user-provider';
import { useSettings } from '~/context/setting-provider';
import enUS from 'antd/lib/calendar/locale/en_US';
import zhCN from 'antd/lib/locale/zh_CN';
import type { Locale } from 'antd/lib/locale';

export type MenuItem = {
  key: string;
  name: string;
  icon: React.ReactNode;
  path: string;
  hideInMenu?: boolean;
  children?: MenuItem[];
};

const defaultMenus: MenuItem[] = [
  {
    key: 'home',
    path: '/home',
    name: 'Home',
    icon: <HomeOutlined />,
    children: [
      {
        key: 'workbench',
        path: '/home/<USER>',
        name: 'Workbench',
        icon: <DashboardOutlined />,
        children: [
          {
            key: 'data-overview',
            path: '/home/<USER>/data-overview',
            name: 'Data Overview',
            icon: <AreaChartOutlined />,
          },
          {
            key: 'todo-items',
            path: '/home/<USER>/todo-items',
            name: 'To-Do Items',
            icon: <CheckCircleOutlined />,
          },
          {
            key: 'quick-access',
            path: '/home/<USER>/quick-access',
            name: 'Quick Access',
            icon: <AppstoreOutlined />,
          },
        ],
      },
    ],
  },
  {
    key: 'system-settings',
    name: 'System Settings',
    icon: <SettingOutlined />,
    path: '/system-settings',
    children: [
      {
        key: 'ui-customization',
        name: 'UI Customization',
        path: '/system-settings/ui-customization',
        icon: <PictureOutlined />,
        children: [
          {
            key: 'interface-theme',
            path: '/system-settings/ui-customization/interface-theme',
            name: 'Interface Theme',
            icon: <PictureOutlined />,
          },
          {
            key: 'login-page-management',
            path: '/system-settings/ui-customization/login-page',
            name: 'Login Page Management',
            icon: <UserOutlined />,
          },
        ],
      },
      {
        key: 'system-parameters',
        name: 'System Parameters',
        path: '/system-settings/parameters',
        icon: <ToolOutlined />,
        children: [
          {
            key: 'configuration-settings',
            path: '/system-settings/parameters/configuration',
            name: 'Configuration Settings',
            icon: <SettingOutlined />,
          },
          {
            key: 'multi-environment-management',
            path: '/system-settings/parameters/environments',
            name: 'Multi-Environment Management',
            icon: <GlobalOutlined />,
          },
        ],
      },
      {
        key: 'extension-functions',
        name: 'Extension Functions',
        path: '/system-settings/extensions',
        icon: <AppstoreOutlined />,
        children: [
          {
            key: 'dictionary-management',
            path: '/system-settings/extensions/dictionary',
            name: 'Dictionary Management',
            icon: <BookOutlined />,
          },
          {
            key: 'license-management',
            path: '/system-settings/extensions/license',
            name: 'License Management',
            icon: <SafetyCertificateOutlined />,
          },
        ],
      },
      {
        key: 'menu-management',
        name: 'Menu Management',
        path: '/system-settings/menu',
        icon: <AppstoreOutlined />,
        children: [
          {
            key: 'menu-categories',
            path: '/system-settings/menu/categories',
            name: 'Menu Categories',
            icon: <AppstoreOutlined />,
          },
          {
            key: 'menu-items',
            path: '/system-settings/menu/items',
            name: 'Menu Items',
            icon: <AppstoreOutlined />,
          },
        ],
      },
    ],
  },
  {
    key: 'organization-management',
    name: 'Organization and Personnel Management',
    icon: <TeamOutlined />,
    path: '/organization',
    children: [
      {
        key: 'organization-structure',
        name: 'Organization Structure',
        path: '/organization/structure',
        icon: <BankOutlined />,
        children: [
          {
            key: 'company-structure',
            path: '/organization/structure/company',
            name: 'Company Structure',
            icon: <BankOutlined />,
          },
          {
            key: 'department-information',
            path: '/organization/structure/department',
            name: 'Department Information',
            icon: <TeamOutlined />,
          },
          {
            key: 'post-rank-management',
            path: '/organization/structure/post-rank',
            name: 'Post and Rank Management',
            icon: <SolutionOutlined />,
          },
          {
            key: 'virtual-teams',
            path: '/organization/structure/virtual-teams',
            name: 'Virtual Teams',
            icon: <UsergroupAddOutlined />,
          },
        ],
      },
      {
        key: 'personnel-management',
        name: 'Personnel Management',
        path: '/organization/personnel',
        icon: <UserOutlined />,
        children: [
          {
            key: 'employee-information',
            path: '/organization/personnel/employee',
            name: 'Employee Information',
            icon: <IdcardOutlined />,
          },
          {
            key: 'post-adjustment-records',
            path: '/organization/personnel/post-adjustment',
            name: 'Post Adjustment Records',
            icon: <FileTextOutlined />,
          },
          {
            key: 'organization-chart',
            path: '/organization/personnel/chart',
            name: 'Organization Chart',
            icon: <TeamOutlined />,
          },
        ],
      },
    ],
  },
  {
    key: 'permissions-center',
    name: 'Permissions Center',
    icon: <KeyOutlined />,
    path: '/permissions',
    children: [
      {
        key: 'permissions-management',
        name: 'Permissions Management',
        path: '/permissions/management',
        icon: <LockOutlined />,
        children: [
          {
            key: 'api-permissions',
            path: '/permissions/management/api',
            name: 'API Permissions',
            icon: <ApiOutlined />,
          },
          {
            key: 'functional-permissions',
            path: '/permissions/management/functional',
            name: 'Functional Permissions',
            icon: <SafetyOutlined />,
          },
          {
            key: 'menu-permissions',
            path: '/permissions/management/menu',
            name: 'Menu Permissions',
            icon: <AppstoreOutlined />,
          },
        ],
      },
      {
        key: 'roles-users',
        name: 'Roles and Users',
        path: '/permissions/roles-users',
        icon: <TeamOutlined />,
        children: [
          {
            key: 'role-management',
            path: '/permissions/roles-users/roles',
            name: 'Role Management',
            icon: <SafetyOutlined />,
          },
          {
            key: 'user-group-management',
            path: '/permissions/roles-users/groups',
            name: 'User Group Management',
            icon: <UsergroupAddOutlined />,
          },
          {
            key: 'user-management',
            path: '/permissions/roles-users/users',
            name: 'User Management',
            icon: <UserOutlined />,
          },
        ],
      },
      {
        key: 'permissions-audit',
        name: 'Permissions Audit',
        path: '/permissions/audit',
        icon: <AuditOutlined />,
        children: [
          {
            key: 'conflict-detection',
            path: '/permissions/audit/conflict',
            name: 'Conflict Detection',
            icon: <BugOutlined />,
          },
          {
            key: 'impact-analysis',
            path: '/permissions/audit/impact',
            name: 'Impact Analysis',
            icon: <AreaChartOutlined />,
          },
        ],
      },
    ],
  },
  {
    key: 'security-center',
    name: 'Security Center',
    icon: <SecurityScanOutlined />,
    path: '/security',
    children: [
      {
        key: 'audit-logs',
        name: 'Audit Logs',
        path: '/security/audit-logs',
        icon: <FileTextOutlined />,
        children: [
          {
            key: 'online-users',
            path: '/security/audit-logs/online-users',
            name: 'Online Users',
            icon: <UserOutlined />,
          },
          {
            key: 'operation-logs',
            path: '/security/audit-logs/operations',
            name: 'Operation Logs',
            icon: <FileTextOutlined />,
          },
          {
            key: 'login-logs',
            path: '/security/audit-logs/login',
            name: 'Login Logs',
            icon: <UserOutlined />,
          },
          {
            key: 'audit-reports',
            path: '/security/audit-logs/reports',
            name: 'Audit Reports',
            icon: <AreaChartOutlined />,
          },
        ],
      },
      {
        key: 'risk-control',
        name: 'Risk Control',
        path: '/security/risk-control',
        icon: <SafetyCertificateOutlined />,
        children: [
          {
            key: 'anomaly-login-detection',
            path: '/security/risk-control/anomaly-login',
            name: 'Anomaly Login Detection',
            icon: <AlertOutlined />,
          },
          {
            key: 'password-security-check',
            path: '/security/risk-control/password',
            name: 'Password Security Check',
            icon: <LockOutlined />,
          },
          {
            key: 'ip-whitelist',
            path: '/security/risk-control/ip-whitelist',
            name: 'IP Whitelist Configuration',
            icon: <ToolOutlined />,
          },
          {
            key: 'security-baseline',
            path: '/security/risk-control/baseline',
            name: 'Security Baseline Configuration',
            icon: <SafetyOutlined />,
          },
        ],
      },
    ],
  },
  {
    key: 'monitoring-center',
    name: 'Monitoring Center',
    icon: <DashboardOutlined />,
    path: '/monitoring',
    children: [
      {
        key: 'service-monitoring',
        name: 'Service Monitoring',
        path: '/monitoring/service',
        icon: <CloudOutlined />,
        children: [
          {
            key: 'service-status',
            path: '/monitoring/service/status',
            name: 'Service Status',
            icon: <CheckCircleOutlined />,
          },
          {
            key: 'interface-statistics',
            path: '/monitoring/service/interface',
            name: 'Interface Statistics',
            icon: <BarChartOutlined />,
          },
          {
            key: 'chain-tracing',
            path: '/monitoring/service/tracing',
            name: 'Chain Tracing',
            icon: <NodeIndexOutlined />,
          },
          {
            key: 'server-monitoring',
            path: '/monitoring/service/server',
            name: 'Server Monitoring',
            icon: <DesktopOutlined />,
          },
          {
            key: 'alert-rules',
            path: '/monitoring/service/alerts',
            name: 'Alert Rules',
            icon: <AlertOutlined />,
          },
        ],
      },
      {
        key: 'performance-monitoring',
        name: 'Performance Monitoring',
        path: '/monitoring/performance',
        icon: <AreaChartOutlined />,
        children: [
          {
            key: 'slow-sql-analysis',
            path: '/monitoring/performance/slow-sql',
            name: 'Slow SQL Analysis',
            icon: <DatabaseOutlined />,
          },
          {
            key: 'cache-monitoring',
            path: '/monitoring/performance/cache',
            name: 'Cache Monitoring',
            icon: <ThunderboltOutlined />,
          },
          {
            key: 'message-queue-monitoring',
            path: '/monitoring/performance/queue',
            name: 'Message Queue Monitoring',
            icon: <MailOutlined />,
          },
          {
            key: 'storage-space-monitoring',
            path: '/monitoring/performance/storage',
            name: 'Storage Space Monitoring',
            icon: <HddOutlined />,
          },
        ],
      },
    ],
  },
  {
    key: 'file-management',
    name: 'File Management',
    icon: <FileOutlined />,
    path: '/file',
    children: [
      {
        key: 'file-service',
        name: 'File Service',
        path: '/file/service',
        icon: <FileOutlined />,
        children: [
          {
            key: 'storage-policies',
            path: '/file/service/storage-policies',
            name: 'Storage Policies',
            icon: <HddOutlined />,
          },
          {
            key: 'access-permissions',
            path: '/file/service/access-permissions',
            name: 'Access Permissions Configuration',
            icon: <LockOutlined />,
          },
          {
            key: 'version-control',
            path: '/file/service/version-control',
            name: 'Version Control',
            icon: <BranchesOutlined />,
          },
          {
            key: 'recycle-bin',
            path: '/file/service/recycle-bin',
            name: 'Recycle Bin Management',
            icon: <DeleteOutlined />,
          },
          {
            key: 'document-library',
            path: '/file/service/document-library',
            name: 'Document Library',
            icon: <FolderOutlined />,
          },
        ],
      },
    ],
  },
  {
    key: 'message-center',
    name: 'Message Center',
    icon: <MailOutlined />,
    path: '/message',
    children: [
      {
        key: 'message-configuration',
        name: 'Message Configuration',
        path: '/message/configuration',
        icon: <SettingOutlined />,
        children: [
          {
            key: 'template-management',
            path: '/message/configuration/templates',
            name: 'Template Management',
            icon: <FileTextOutlined />,
          },
          {
            key: 'notification-channel-settings',
            path: '/message/configuration/channels',
            name: 'Notification Channel Settings',
            icon: <NotificationOutlined />,
          },
          {
            key: 'signature-management',
            path: '/message/configuration/signatures',
            name: 'Signature Management',
            icon: <EditOutlined />,
          },
          {
            key: 'priority-settings',
            path: '/message/configuration/priorities',
            name: 'Priority Settings',
            icon: <OrderedListOutlined />,
          },
        ],
      },
      {
        key: 'message-operations',
        name: 'Message Operations',
        path: '/message/operations',
        icon: <BellOutlined />,
        children: [
          {
            key: 'unread-reminders',
            path: '/message/operations/unread',
            name: 'Unread Reminders',
            icon: <BellOutlined />,
          },
          {
            key: 'category-view',
            path: '/message/operations/categories',
            name: 'Category View',
            icon: <AppstoreOutlined />,
          },
          {
            key: 'sending-records',
            path: '/message/operations/records',
            name: 'Sending Records',
            icon: <FileTextOutlined />,
          },
          {
            key: 'archive-management',
            path: '/message/operations/archive',
            name: 'Archive Management',
            icon: <InboxOutlined />,
          },
        ],
      },
      {
        key: 'message-analytics',
        name: 'Message Analytics',
        path: '/message/analytics',
        icon: <AreaChartOutlined />,
        children: [
          {
            key: 'delivery-metrics',
            path: '/message/analytics/delivery',
            name: 'Delivery Metrics',
            icon: <PieChartOutlined />,
          },
          {
            key: 'engagement-tracking',
            path: '/message/analytics/engagement',
            name: 'Engagement Tracking',
            icon: <LineChartOutlined />,
          },
        ],
      },
    ],
  },
  {
    key: 'internationalization',
    name: 'Internationalization Settings',
    icon: <GlobalOutlined />,
    path: '/internationalization',
    children: [
      {
        key: 'multi-language',
        name: 'Multi-Language Management',
        path: '/internationalization/language',
        icon: <TranslationOutlined />,
        children: [
          {
            key: 'language-pack-management',
            path: '/internationalization/language/packs',
            name: 'Language Pack Management',
            icon: <BookOutlined />,
          },
          {
            key: 'realtime-translation',
            path: '/internationalization/language/translation',
            name: 'Real-Time Translation Tool',
            icon: <TranslationOutlined />,
          },
          {
            key: 'cultural-adaptation',
            path: '/internationalization/language/cultural',
            name: 'Cultural Adaptation Configuration',
            icon: <GlobalOutlined />,
          },
          {
            key: 'regional-format',
            path: '/internationalization/language/format',
            name: 'Regional Format Settings',
            icon: <FileTextOutlined />,
          },
          {
            key: 'rtl-support',
            path: '/internationalization/language/rtl',
            name: 'Right-to-Left (RTL) Support',
            icon: <SwapOutlined />,
          },
        ],
      },
      {
        key: 'timezone-management',
        name: 'Time Zone Management',
        path: '/internationalization/timezone',
        icon: <ClockCircleOutlined />,
        children: [
          {
            key: 'global-personal-timezones',
            path: '/internationalization/timezone/zones',
            name: 'Global and Personal Time Zones',
            icon: <GlobalOutlined />,
          },
          {
            key: 'time-format',
            path: '/internationalization/timezone/format',
            name: 'Time Format Settings',
            icon: <FieldTimeOutlined />,
          },
          {
            key: 'holiday-configuration',
            path: '/internationalization/timezone/holidays',
            name: 'Holiday Configuration',
            icon: <CalendarOutlined />,
          },
        ],
      },
    ],
  },
  {
    key: 'operations-tools',
    name: 'Operations and Tools',
    icon: <ToolOutlined />,
    path: '/operations',
    children: [
      {
        key: 'system-maintenance',
        name: 'System Maintenance',
        path: '/operations/maintenance',
        icon: <ToolOutlined />,
        children: [
          {
            key: 'data-backup-recovery',
            path: '/operations/maintenance/backup',
            name: 'Data Backup and Recovery',
            icon: <SaveOutlined />,
          },
          {
            key: 'system-upgrade',
            path: '/operations/maintenance/upgrade',
            name: 'System Upgrade Management',
            icon: <UploadOutlined />,
          },
          {
            key: 'cache-cleanup',
            path: '/operations/maintenance/cache',
            name: 'Cache Cleanup Tool',
            icon: <ClearOutlined />,
          },
        ],
      },
      {
        key: 'data-migration',
        name: 'Data Migration',
        path: '/operations/migration',
        icon: <ImportOutlined />,
        children: [
          {
            key: 'migration-task',
            path: '/operations/migration/tasks',
            name: 'Migration Task Management',
            icon: <CarryOutOutlined />,
          },
          {
            key: 'data-cleaning',
            path: '/operations/migration/cleaning',
            name: 'Data Cleaning Configuration',
            icon: <ClearOutlined />,
          },
          {
            key: 'log-query',
            path: '/operations/migration/logs',
            name: 'Log Query',
            icon: <FileSearchOutlined />,
          },
          {
            key: 'consistency-validation',
            path: '/operations/migration/validation',
            name: 'Consistency Validation',
            icon: <CheckCircleOutlined />,
          },
        ],
      },
    ],
  },
  {
    key: 'help-center',
    name: 'Help Center',
    icon: <QuestionCircleOutlined />,
    path: '/help',
    children: [
      {
        key: 'system-documentation',
        name: 'System Documentation',
        path: '/help/documentation',
        icon: <BookOutlined />,
        children: [
          {
            key: 'operation-manuals',
            path: '/help/documentation/manuals',
            name: 'Operation Manuals',
            icon: <FileTextOutlined />,
          },
          {
            key: 'api-documentation',
            path: '/help/documentation/api',
            name: 'API Documentation',
            icon: <ApiOutlined />,
          },
          {
            key: 'faqs',
            path: '/help/documentation/faqs',
            name: 'FAQs',
            icon: <QuestionCircleOutlined />,
          },
          {
            key: 'changelog',
            path: '/help/documentation/changelog',
            name: 'Changelog',
            icon: <HistoryOutlined />,
          },
          {
            key: 'knowledge-base',
            path: '/help/documentation/knowledge-base',
            name: 'Searchable Knowledge Base',
            icon: <SearchOutlined />,
          },
        ],
      },
      {
        key: 'technical-support',
        name: 'Technical Support',
        path: '/help/support',
        icon: <CustomerServiceOutlined />,
        children: [
          {
            key: 'online-feedback',
            path: '/help/support/feedback',
            name: 'Online Feedback',
            icon: <CommentOutlined />,
          },
          {
            key: 'ticket-system',
            path: '/help/support/tickets',
            name: 'Ticket System',
            icon: <SolutionOutlined />,
          },
          {
            key: 'support-status',
            path: '/help/support/status',
            name: 'Service Status',
            icon: <CheckCircleOutlined />,
          },
          {
            key: 'contact-admin',
            path: '/help/support/contact',
            name: 'Contact Administrator',
            icon: <PhoneOutlined />,
          },
        ],
      },
    ],
  },
  {
    key: 'personal-center',
    name: 'Personal Center',
    icon: <UserOutlined />,
    path: '/personal',
    children: [
      {
        key: 'personal-information',
        name: 'Personal Information',
        path: '/personal/information',
        icon: <IdcardOutlined />,
      },
      {
        key: 'security-settings',
        name: 'Security Settings',
        path: '/personal/security',
        icon: <SafetyOutlined />,
      },
      {
        key: 'notification-settings',
        name: 'Notification Settings',
        path: '/personal/notifications',
        icon: <BellOutlined />,
      },
      {
        key: 'operation-records',
        name: 'Operation Records',
        path: '/personal/records',
        icon: <FileTextOutlined />,
      },
      {
        key: 'theme-preferences',
        name: 'Theme Preferences',
        path: '/personal/theme',
        icon: <PictureOutlined />,
      },
    ],
  },
  {
    key: 'developer-tools',
    name: 'Developer Tools',
    icon: <CodeOutlined />,
    path: '/developer',
    children: [
      {
        key: 'api-docs',
        name: 'API Documentation',
        path: '/developer/api-docs',
        icon: <ApiOutlined />,
      },
      {
        key: 'interface-testing',
        name: 'Interface Testing',
        path: '/developer/testing',
        icon: <BugOutlined />,
      },
      {
        key: 'code-generator',
        name: 'Code Generator',
        path: '/developer/code-generator',
        icon: <BuildOutlined />,
      },
      {
        key: 'form-builder',
        name: 'Form Builder',
        path: '/developer/form-builder',
        icon: <FormOutlined />,
      },
      {
        key: 'database-management',
        name: 'Database Management',
        path: '/developer/database',
        icon: <DatabaseOutlined />,
      },
      {
        key: 'online-terminal',
        name: 'Online Terminal',
        path: '/developer/terminal',
        icon: <CodeOutlined />,
      },
      {
        key: 'mock-data-generator',
        name: 'Mock Data Generator',
        path: '/developer/mock-data',
        icon: <FundOutlined />,
      },
    ],
  },
];

const MainLayout: React.FC = () => {
  const { user, isLoading, logout } = useUser();
  const location = useLocation();
  const { i18n, t } = useTranslation();
  const navigate = useNavigate();
  const [collapsed, setCollapsed] = React.useState(false);
  const [menuVisible, setMenuVisible] = React.useState(false);
  const { theme: themeConfig, setTheme } = useSettings();
  const [activeTab, setActiveTab] = useState<string>('/');
  const [tabs, setTabs] = useState<{ key: string; label: string; path: string }[]>(() => {
    // Initialize tabs from sessionStorage if available
    const savedTabs = sessionStorage.getItem('mainLayoutTabs');
    return savedTabs ? JSON.parse(savedTabs) : [{ key: '/', label: 'Home', path: '/' }];
  });

  // Save tabs to sessionStorage whenever they change
  useEffect(() => {
    sessionStorage.setItem('mainLayoutTabs', JSON.stringify(tabs));
  }, [tabs]);

  // Add tab when navigating to a new page
  useEffect(() => {
    const currentPath = location.pathname;
    const menuItem = findMenuItemByPath(defaultMenus, currentPath);

    if (menuItem) {
      setTabs(prev => {
        // Check if tab already exists
        const existingTab = prev.find(tab => tab.path === currentPath);
        if (existingTab) {
          return prev;
        }

        // Ensure home tab is always first
        const homeTab = prev.find(tab => tab.path === '/');
        const otherTabs = prev.filter(tab => tab.path !== '/');
        return [homeTab!, ...otherTabs, { key: currentPath, label: menuItem.name, path: currentPath }];
      });
    }
    setActiveTab(currentPath);
  }, [location.pathname]);

  // Helper function to find menu item by path
  const findMenuItemByPath = (items: MenuItem[], path: string): MenuItem | undefined => {
    for (const item of items) {
      if (item.path === path) return item;
      if (item.children) {
        const found = findMenuItemByPath(item.children, path);
        if (found) return found;
      }
    }
    return undefined;
  };

  // Handle tab change
  const handleTabChange = (key: string) => {
    const tab = tabs.find(t => t.key === key);
    if (tab) {
      navigate(tab.path);
    }
  };

  // Handle tab close
  const handleTabEdit = (targetKey: string, action: 'add' | 'remove') => {
    if (action === 'remove' && targetKey !== '/') {
      // Prevent closing home tab
      setTabs(prev => prev.filter(tab => tab.key !== targetKey));
      if (activeTab === targetKey) {
        const newActiveTab = tabs.find(tab => tab.key !== targetKey)?.key || '/';
        navigate(newActiveTab);
      }
    }
  };

  // Function to toggle between light and dark mode
  const toggleTheme = () => {
    setTheme({
      ...themeConfig,
      navTheme: themeConfig.navTheme === 'light' ? 'realDark' : 'light',
    });
  };

  // Function to handle language change by setting sessionStorage
  const handleLanguageChange = (lang: string) => {
    sessionStorage.setItem('preferred-language', lang);
    i18n.changeLanguage(lang);
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Profile',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Settings',
      onClick: () => {
        console.log('settings');
      },
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
      onClick: () => logout(),
    },
  ];

  const languageMenuItems = [
    {
      key: 'en-US',
      label: <span>English {i18n.language === 'en-US' && '✓'}</span>,
      onClick: () => handleLanguageChange('en-US'),
    },
    {
      key: 'zh-CN',
      label: <span>中文 {i18n.language === 'zh-CN' && '✓'}</span>,
      onClick: () => handleLanguageChange('zh-CN'),
    },
  ];

  const getCurrentSectionMenu = () => {
    const currentPath = location.pathname;
    const currentSection = defaultMenus.find(
      item =>
        currentPath === item.path ||
        currentPath.startsWith(item.path + '/') ||
        item.children?.some(child => currentPath === child.path || currentPath.startsWith(child.path + '/'))
    );

    return currentSection?.children || defaultMenus[0].children || [];
  };

  const getAntLocale = () => {
    switch (i18n.language) {
      case 'zh-CN':
        return zhCN;
      case 'en-US':
      default:
        return enUS;
    }
  };

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: themeConfig.colorPrimary,
        },
      }}
      locale={getAntLocale() as Locale}
    >
      <AntdApp>
        <ProLayout
          layout={themeConfig.layout}
          navTheme={themeConfig.navTheme}
          location={location}
          menu={{ defaultOpenAll: true }}
          siderWidth={220}
          disableMobile={true}
          fixSiderbar={true}
          fixedHeader={true}
          collapsed={collapsed}
          onCollapse={setCollapsed}
          style={{ height: '100vh', backgroundColor: 'white' }}
          menuDataRender={() => getCurrentSectionMenu()}
          menuItemRender={(menuItemProps, defaultDom) => {
            if (menuItemProps.isUrl || !menuItemProps.path) {
              return <span>{defaultDom}</span>;
            }
            return (
              <Link to={menuItemProps.path}>
                <span>{defaultDom}</span>
              </Link>
            );
          }}
          headerTitleRender={(logo, title, props) => {
            return (
              <div className='flex items-center'>
                <Dropdown
                  open={menuVisible}
                  onOpenChange={setMenuVisible}
                  trigger={['click']}
                  placement='bottomLeft'
                  dropdownRender={() => (
                    <MenuPanelDropdown
                      menus={defaultMenus}
                      onClose={() => setMenuVisible(false)}
                    />
                  )}
                  overlayStyle={{
                    left: 0,
                    top: '48px', // Adjust this value based on your header height
                    position: 'fixed',
                    width: '100%',
                  }}
                >
                  {menuVisible ? (
                    <CloseOutlined
                      className='mr-4 flex cursor-pointer items-center px-4 py-5 text-lg'
                      style={{
                        color: themeConfig.navTheme === 'light' ? '#000000' : '#ffffff',
                      }}
                      onMouseEnter={e => {
                        e.currentTarget.style.backgroundColor =
                          themeConfig.navTheme === 'light' ? '#EBF5FF' : '#333333';
                        e.currentTarget.style.borderRadius = '0';
                      }}
                      onMouseLeave={e => {
                        e.currentTarget.style.backgroundColor = '';
                      }}
                      onClick={() => setMenuVisible(!menuVisible)}
                    />
                  ) : (
                    <MenuOutlined
                      className='mr-4 flex cursor-pointer items-center px-4 py-5 text-lg'
                      style={{
                        color: themeConfig.navTheme === 'light' ? '#000000' : '#ffffff',
                      }}
                      onMouseEnter={e => {
                        e.currentTarget.style.backgroundColor =
                          themeConfig.navTheme === 'light' ? '#EBF5FF' : '#333333';
                        e.currentTarget.style.borderRadius = '0';
                      }}
                      onMouseLeave={e => {
                        e.currentTarget.style.backgroundColor = '';
                      }}
                      onClick={() => setMenuVisible(!menuVisible)}
                    />
                  )}
                </Dropdown>
                <Link
                  to='/'
                  className='flex items-center'
                >
                  <span>{logo}</span>
                  <span>{title}</span>
                </Link>
                <div
                  className='ml-4 flex cursor-pointer items-center px-4 py-5 text-lg'
                  style={{
                    color: themeConfig.navTheme === 'light' ? '#000000' : '#ffffff',
                  }}
                  onMouseEnter={e => {
                    e.currentTarget.style.backgroundColor = themeConfig.navTheme === 'light' ? '#EBF5FF' : '#333333';
                    e.currentTarget.style.borderRadius = '0';
                  }}
                  onMouseLeave={e => {
                    e.currentTarget.style.backgroundColor = '';
                  }}
                  onClick={() => navigate('/')}
                >
                  <HomeOutlined className='text-lg' />
                </div>
              </div>
            );
          }}
          actionsRender={() => [
            <div
              key='theme-toggle'
              className='flex h-full cursor-pointer items-center px-4 py-5 text-lg'
              style={{
                color: themeConfig.navTheme === 'light' ? '#000000' : '#ffffff',
              }}
              onMouseEnter={e => {
                e.currentTarget.style.backgroundColor = themeConfig.navTheme === 'light' ? '#EBF5FF' : '#333333';
                e.currentTarget.style.borderRadius = '0';
                // Apply height to match header height
                const headerHeight = e.currentTarget.closest('.ant-layout-header')?.clientHeight;
                if (headerHeight) {
                  e.currentTarget.style.height = `${headerHeight}px`;
                }
              }}
              onMouseLeave={e => {
                e.currentTarget.style.backgroundColor = '';
              }}
              onClick={toggleTheme}
              title={themeConfig.navTheme === 'light' ? 'Switch to Dark Mode' : 'Switch to Light Mode'}
            >
              {themeConfig.navTheme === 'light' ? (
                <MoonOutlined className='text-lg' />
              ) : (
                <SunOutlined className='text-lg' />
              )}
            </div>,
            <Dropdown
              key='language'
              menu={{ items: languageMenuItems }}
              placement='bottomRight'
              destroyPopupOnHide={true}
            >
              <div
                className='flex h-full cursor-pointer items-center px-4 py-5 text-lg'
                style={{
                  color: themeConfig.navTheme === 'light' ? '#000000' : '#ffffff',
                }}
                onMouseEnter={e => {
                  e.currentTarget.style.backgroundColor = themeConfig.navTheme === 'light' ? '#EBF5FF' : '#333333';
                  e.currentTarget.style.borderRadius = '0';
                  // Apply height to match header height
                  const headerHeight = e.currentTarget.closest('.ant-layout-header')?.clientHeight;
                  if (headerHeight) {
                    e.currentTarget.style.height = `${headerHeight}px`;
                  }
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.backgroundColor = '';
                }}
              >
                {i18n.language === 'en-US' ? (
                  <EnglishIcon className='text-lg' />
                ) : (
                  <TranslationOutlined className='text-lg' />
                )}
              </div>
            </Dropdown>,
            <Dropdown
              key='user-dropdown'
              menu={{ items: userMenuItems }}
              placement='bottomRight'
              destroyPopupOnHide={true}
            >
              <div
                className='flex h-full cursor-pointer items-center px-4 py-5'
                style={{
                  color: themeConfig.navTheme === 'light' ? '#000000' : '#ffffff',
                }}
                onMouseEnter={e => {
                  e.currentTarget.style.backgroundColor = themeConfig.navTheme === 'light' ? '#EBF5FF' : '#333333';
                  e.currentTarget.style.borderRadius = '0';
                  // Apply height to match header height
                  const headerHeight = e.currentTarget.closest('.ant-layout-header')?.clientHeight;
                  if (headerHeight) {
                    e.currentTarget.style.height = `${headerHeight}px`;
                  }
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.backgroundColor = '';
                }}
              >
                <Space>
                  <Avatar
                    size='small'
                    src={
                      user?.picture || 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png'
                    }
                  />
                  <span>{isLoading ? 'Loading...' : user?.nickname || user?.name || user?.sub || 'Guest'}</span>
                </Space>
              </div>
            </Dropdown>,
          ]}
        >
          <div className='tailwind-content h-full w-full'>
            <Tabs
              activeKey={activeTab}
              onChange={handleTabChange}
              onEdit={(targetKey, action) => handleTabEdit(targetKey as string, action)}
              type='editable-card'
              hideAdd
              items={tabs.map(tab => ({
                key: tab.key,
                label: tab.label,
                closable: tab.path !== '/',
              }))}
            />
            <div className='p-4'>
              <Outlet />
            </div>
          </div>
        </ProLayout>

        {/* <SettingDrawer
          settings={{
            navTheme: themeConfig.navTheme,
            colorPrimary: themeConfig.colorPrimary,
            layout: themeConfig.layout,
            contentWidth: 'Fluid',
            fixedHeader: true,
            fixSiderbar: true,
            splitMenus: false,
          }}
          hideHintAlert={true}
          hideCopyButton={true}
          enableDarkTheme
          onSettingChange={settings => {
            const { navTheme, colorPrimary } = settings;
            // Only update navTheme and colorPrimary
            setTheme({
              ...themeConfig,
              navTheme,
              colorPrimary,
            });
          }}
          disableUrlParams
          // Remove the drawerProps as we're using our own Drawer component
        /> */}
      </AntdApp>
    </ConfigProvider>
  );
};

export default MainLayout;
