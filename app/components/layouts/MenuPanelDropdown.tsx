import type { MenuProps } from 'antd';
import React, { useState } from 'react';
import { Link } from 'react-router';
import { useSettings } from '~/context/setting-provider';

import type { MenuItem } from './MainLayout';

// Create a responsive menu panel
const MenuPanelDropdown: React.FC<{
  menus: MenuItem[];
  onClose?: () => void;
}> = ({ menus, onClose }) => {
  const { theme } = useSettings();
  const [selectedCategory, setSelectedCategory] = useState('system-admin');

  // Get primary color and a lighter version of it for hover states
  const colorPrimary = theme.colorPrimary;
  const primaryColorLight = `${colorPrimary}20`; // 20% opacity

  // Categories for the list box
  const categories = [
    { id: 'system-admin', name: 'System Admin' },
    { id: 'file', name: 'File Management' },
    { id: 'message', name: 'Messaging' },
    { id: 'personal', name: 'Personal Center' },
    { id: 'developer', name: 'Developer Tools' },
    { id: 'help', name: 'Help Center' },
    { id: 'other', name: 'Other Features' },
  ];

  // Map categories to menu keys (one category has many menu items)
  const categoryMenuMap: Record<string, string[]> = {
    'system-admin': [
      'system-settings',
      'permissions-center',
      'monitoring',
      'file-management',
      'organization-management',
      'internationalization',
      'operations-tools',
      'permissions-center',
      'security-center',
      'monitoring-center',
    ],
    'file': ['file-management'],
    'message': ['message-center'],
    'help': ['help-center'],
    'personal': ['personal-center'],
    'developer': ['developer-tools'],
    'other': ['home'],
  };

  // Filter menu items by category
  const getMenuItemsByCategory = (categoryId: string): MenuItem[] => {
    const menuKeys = categoryMenuMap[categoryId] || [];
    return menus.filter(item => menuKeys.includes(item.key));
  };

  // Determine how many menus to place in each responsive column
  const renderMenuItems = (items: MenuItem[]) => {
    return items.map(item => {
      const hasChildren = item.children && item.children.length > 0;

      return (
        <div
          key={item.key}
          className='mb-5'
        >
          {/* Parent item */}
          <Link
            to={item.path}
            className='group mb-3 flex items-center gap-2 text-base font-medium transition-colors'
            style={{ color: '#000000' }}
            onClick={onClose}
          >
            <span
              className='text-lg'
              style={{ color: '#000000' }}
            >
              {item.icon}
            </span>
            <span className='whitespace-nowrap transition-transform group-hover:translate-x-0.5'>{item.name}</span>
          </Link>

          {/* Children items */}
          {hasChildren && (
            <div className='space-y-2 pl-6'>
              {item.children?.map(child => (
                <div
                  key={child.key}
                  className='mb-2'
                >
                  <Link
                    to={child.path}
                    className='group flex items-center gap-2 text-sm transition-colors'
                    style={{
                      color: '#333333',
                    }}
                    onMouseOver={e => {
                      e.currentTarget.style.color = colorPrimary;
                    }}
                    onMouseOut={e => {
                      e.currentTarget.style.color = '#333333';
                    }}
                    onClick={onClose}
                  >
                    <span
                      className='text-base transition-colors'
                      style={{ color: '#555555' }}
                      onMouseOver={e => {
                        e.currentTarget.style.color = colorPrimary;
                      }}
                      onMouseOut={e => {
                        e.currentTarget.style.color = '#555555';
                      }}
                    >
                      {child.icon}
                    </span>
                    <span className='whitespace-nowrap transition-transform group-hover:translate-x-0.5'>
                      {child.name}
                    </span>
                  </Link>

                  {/* Grandchildren items */}
                  {child.children && child.children.length > 0 && (
                    <div className='mt-2 space-y-1.5 pl-6'>
                      {child.children.map(grandChild => (
                        <Link
                          key={grandChild.key}
                          to={grandChild.path}
                          className='group flex items-center gap-1.5 text-xs transition-colors'
                          style={{
                            color: '#666666',
                          }}
                          onMouseOver={e => {
                            e.currentTarget.style.color = colorPrimary;
                          }}
                          onMouseOut={e => {
                            e.currentTarget.style.color = '#666666';
                          }}
                          onClick={onClose}
                        >
                          <span
                            className='text-sm transition-colors'
                            style={{ color: '#777777' }}
                            onMouseOver={e => {
                              e.currentTarget.style.color = colorPrimary;
                            }}
                            onMouseOut={e => {
                              e.currentTarget.style.color = '#777777';
                            }}
                          >
                            {grandChild.icon}
                          </span>
                          <span className='whitespace-nowrap transition-transform group-hover:translate-x-0.5'>
                            {grandChild.name}
                          </span>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      );
    });
  };

  // Create balanced columns from menu items
  const createResponsiveColumns = () => {
    // Get menu items for the selected category
    const categoryMenuItems = getMenuItemsByCategory(selectedCategory);

    // Render all items in a responsive grid
    return (
      <div className='grid grid-cols-1 gap-x-8 gap-y-8 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4'>
        {categoryMenuItems.map(item => (
          <div
            key={item.key}
            className='min-w-[250px] px-4'
          >
            <div className='pr-2 pl-2'>{renderMenuItems([item])}</div>
          </div>
        ))}
      </div>
    );
  };

  // Render the category list box
  const renderCategoryList = () => {
    return (
      <div className='w-64 border-r border-gray-100 pr-4'>
        <div className='mb-4 text-base font-medium text-black'>Categories</div>
        <div className='space-y-1'>
          {categories.map(category => (
            <div
              key={category.id}
              className={`cursor-pointer rounded px-3 py-2 transition-colors hover:bg-gray-50`}
              style={{
                color: selectedCategory === category.id ? colorPrimary : '#333333',
                fontWeight: selectedCategory === category.id ? 500 : 400,
                backgroundColor: selectedCategory === category.id ? primaryColorLight : 'transparent',
              }}
              onClick={() => setSelectedCategory(category.id)}
            >
              {category.name}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div
      className='rounded-b bg-white shadow-lg'
      style={{
        width: '100%',
        maxHeight: '80vh',
        overflowY: 'auto',
        overflowX: 'auto',
        borderTop: `1px solid ${primaryColorLight}`,
      }}
    >
      <div className='container-fluid px-8 py-10'>
        <div className='mx-auto max-w-[2000px]'>
          <div className='flex'>
            {renderCategoryList()}
            <div className='flex-1 pl-6'>{createResponsiveColumns()}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MenuPanelDropdown;
