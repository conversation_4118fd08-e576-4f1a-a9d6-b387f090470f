import { useEffect, useState, type JSX } from 'react';

type ClientOnlyProps = {
  children: () => React.ReactNode;
  fallback?: JSX.Element | null;
};

export function ClientOnly({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
export default ClientOnly;
