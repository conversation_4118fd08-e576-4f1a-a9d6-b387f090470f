// import '@ant-design/v5-patch-for-react-19';
import { PassThrough } from 'node:stream';
import { createCache, extractStyle, StyleProvider } from '@ant-design/cssinjs';
import type { AppLoadContext, EntryContext, unstable_RouterContextProvider } from 'react-router';
import { createReadableStreamFromReadable } from '@react-router/node';
import { ServerRouter } from 'react-router';
import { isbot } from 'isbot';
import type { RenderToPipeableStreamOptions } from 'react-dom/server';
import { renderToPipeableStream } from 'react-dom/server';
import { StrictMode } from 'react';
import i18next from 'i18next';
import { I18nextProvider } from 'react-i18next';
import { getInstance } from './middleware/i18next.middleware';

// Extend the EntryContext type to pass styles to the document
declare module 'react-router' {
  interface EntryContext {
    antdStyles?: string;
  }
}

export const streamTimeout = 5_000;

export default function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  entryContext: EntryContext,
  routerContext: unstable_RouterContextProvider
  // loadContext: AppLoadContext
  // If you have middleware enabled:
) {
  return new Promise((resolve, reject) => {
    let shellRendered = false;
    let userAgent = request.headers.get('user-agent');

    // Create style cache for SSR
    const cache = createCache();

    // Ensure requests from bots and SPA Mode renders wait for all content to load before responding
    // https://react.dev/reference/react-dom/server/renderToPipeableStream#waiting-for-all-content-to-load-for-crawlers-and-static-generation
    let readyOption: keyof RenderToPipeableStreamOptions =
      (userAgent && isbot(userAgent)) || entryContext.isSpaMode ? 'onAllReady' : 'onShellReady';

    const { pipe, abort } = renderToPipeableStream(
      <StrictMode>
        <I18nextProvider i18n={getInstance(routerContext)}>
          <StyleProvider cache={cache}>
            <ServerRouter
              context={entryContext}
              url={request.url}
            />
          </StyleProvider>
        </I18nextProvider>
      </StrictMode>,
      {
        [readyOption]() {
          shellRendered = true;
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);

          // Extract styles from cache after rendering is complete
          const styleText = extractStyle(cache);

          // Don't add any script tags that might cause hydration mismatches
          // We'll use a different approach to inject styles

          responseHeaders.set('Content-Type', 'text/html');

          resolve(
            new Response(stream, {
              headers: responseHeaders,
              status: responseStatusCode,
            })
          );

          pipe(body);

          // After piping the HTML, find and modify the style tag to inject our styles
          // This will replace the empty style tag in the HTML with the actual styles
          setTimeout(() => {
            if (styleText) {
              // Inject Ant Design styles via a post-rendering transform
              // Since this happens after React rendering, it won't cause hydration mismatches
              const styleTagRegex = /<style id="antd-styles"[^>]*><\/style>/;
              const fullHtml = body.toString();
              if (fullHtml && styleTagRegex.test(fullHtml)) {
                const updatedHtml = fullHtml.replace(
                  styleTagRegex,
                  `<style id="antd-styles" data-ssr-styles="true">${styleText}</style>`
                );
                body.write(updatedHtml);
              }
            }
          }, 0);
        },
        onShellError(error: unknown) {
          reject(error);
        },
        onError(error: unknown) {
          responseStatusCode = 500;
          // Log streaming rendering errors from inside the shell.  Don't log
          // errors encountered during initial shell rendering since they'll
          // reject and get logged in handleDocumentRequest.
          if (shellRendered) {
            console.error(error);
          }
        },
      }
    );

    // Abort the rendering stream after the `streamTimeout` so it has time to
    // flush down the rejected boundaries
    setTimeout(abort, streamTimeout + 1000);
  });
}
