import { createCookie } from 'react-router';
import { unstable_createI18nextMiddleware } from 'remix-i18next/middleware';
import { initReactI18next } from 'react-i18next';
import enUS from '~/locales/en-US';
import zhCN from '~/locales/zh-CN';

export const localeCookie = createCookie('lng', {
  path: '/',
  sameSite: 'lax',
  secure: process.env.NODE_ENV === 'production',
  httpOnly: true,
});

export const [i18nextMiddleware, getLocale, getInstance] = unstable_createI18nextMiddleware({
  detection: {
    supportedLanguages: ['en-US', 'zh-CN'],
    fallbackLanguage: 'en-US',
    cookie: localeCookie,
  },
  i18next: {
    resources: { 'en-US': { translation: enUS }, 'zh-CN': { translation: zhCN } },
  },
  plugins: [initReactI18next],
});
