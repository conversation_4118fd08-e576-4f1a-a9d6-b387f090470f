import { redirect } from 'react-router';
import type { Route } from '../+types/root';
import { fetchUserInfo, getSavedUserInfo, logout } from '~/service/auth.service';
import userContext from '~/context/user-context';

const authMiddleware: Route.unstable_ClientMiddlewareFunction = async ({ request, context }, next) => {
  try {
    // First check if we already have the user in context
    let userInfo = context.get(userContext);

    // If not in context, check session storage (which fetchUserInfo will do internally)
    // if (!userInfo) {
    //   userInfo = await fetchUserInfo();
    // }

    console.log('userInfo', userInfo);

    //   // Set user info in context
    //   if (userInfo) {
    //     try {
    //       context.set(userContext, userInfo);
    //     } catch (e) {
    //       console.warn('Could not set user context:', e);
    //     }
    //   }

    //   return await next();
  } catch (error) {
    console.error('Error fetching user info:', error);

    // Redirect to login page on auth error
    // For server-side rendering, use redirect
    if (typeof window === 'undefined') {
      throw redirect('http://localhost:8080');
    }

    //   // For client-side rendering, use window.location for a full page reload
    //   window.location.href = 'http://localhost:8080';

    // We still need to return something for the middleware pipeline
    return next();
  }
};

export default authMiddleware;
