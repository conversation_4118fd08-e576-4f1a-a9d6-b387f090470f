import '@ant-design/v5-patch-for-react-19';

import {
  isRouteErrorResponse,
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useLoaderData,
  type LoaderFunctionArgs,
} from 'react-router';
import { StyleProvider } from '@ant-design/cssinjs';
import { useEffect } from 'react';

import type { Route } from './+types/root';
import './app.css';
import authMiddleware from '~/middleware/auth.middleware';
import { UserProvider } from './context/user-provider';
import { SettingProvider } from './context/setting-provider';
import { ClientOnly } from './components/common/client-only';
import { getLocale, i18nextMiddleware } from './middleware/i18next.middleware';
import { useTranslation } from 'react-i18next';
export const links: Route.LinksFunction = () => [
  { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
  {
    rel: 'preconnect',
    href: 'https://fonts.gstatic.com',
    crossOrigin: 'anonymous',
  },
  {
    rel: 'stylesheet',
    href: 'https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap',
  },
];

export async function loader({ context }: LoaderFunctionArgs) {
  const locale = getLocale(context);
  return { locale };
}

export function Layout({ children }: { children: React.ReactNode }) {
  const { locale } = useLoaderData<typeof loader>();

  return (
    <html lang={locale}>
      <head>
        <meta charSet='utf-8' />
        <meta
          name='viewport'
          content='width=device-width, initial-scale=1'
        />
        <Meta />
        <Links />
      </head>
      <body>
        <UserProvider>
          <SettingProvider>
            <StyleProvider hashPriority='high'>
              <ClientOnly fallback={<></>}>{children}</ClientOnly>
            </StyleProvider>
          </SettingProvider>
        </UserProvider>
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

export default function App() {
  const { locale } = useLoaderData<typeof loader>();
  const { i18n } = useTranslation();

  useEffect(() => {
    const savedLanguage = typeof window !== 'undefined' ? sessionStorage.getItem('preferred-language') : null;
    const preferredLanguage = savedLanguage || locale;

    if (preferredLanguage !== i18n.language) {
      i18n.changeLanguage(preferredLanguage);
    }
  }, [locale, i18n]);

  return <Outlet />;
}

export const unstable_clientMiddleware = [authMiddleware];
export const unstable_middleware = [i18nextMiddleware];

export function ErrorBoundary({ error }: Route.ErrorBoundaryProps) {
  let message = 'Oops!';
  let details = 'An unexpected error occurred.';
  let stack: string | undefined;

  if (isRouteErrorResponse(error)) {
    message = error.status === 404 ? '404' : 'Error';
    details = error.status === 404 ? 'The requested page could not be found.' : error.statusText || details;
  } else if (import.meta.env.DEV && error && error instanceof Error) {
    details = error.message;
    stack = error.stack;
  }

  return (
    <main className='container mx-auto p-4 pt-16'>
      <h1>{message}</h1>
      <p>{details}</p>
      {stack && (
        <pre className='w-full overflow-x-auto p-4'>
          <code>{stack}</code>
        </pre>
      )}
    </main>
  );
}
