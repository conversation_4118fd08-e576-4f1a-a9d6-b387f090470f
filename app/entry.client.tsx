// import '@ant-design/v5-patch-for-react-19';
import { startTransition, StrictMode } from 'react';
import { hydrateRoot } from 'react-dom/client';
import { HydratedRouter } from 'react-router/dom';
import { StyleProvider, createCache } from '@ant-design/cssinjs';
import i18next from 'i18next';
import { I18nextProvider, initReactI18next } from 'react-i18next';
import I18nextBrowserLanguageDetector from 'i18next-browser-languagedetector';
import { getInitialNamespaces } from 'remix-i18next/client';
import Fetch from 'i18next-fetch-backend';

// Remove StrictMode which causes intentional double rendering in development
async function main() {
  await i18next
    .use(initReactI18next)
    .use(I18nextBrowserLanguageDetector)
    .use(Fetch)
    .init({
      fallbackLng: 'en',
      ns: getInitialNamespaces(),
      detection: {
        order: ['htmlTag'],
        caches: [],
        lookupFromPathIndex: 0,
      },
      backend: {
        loadPath: '/locales/{{lng}}/{{ns}}',
      },
    });

  startTransition(() => {
    hydrateRoot(
      document,
      <StrictMode>
        <I18nextProvider i18n={i18next}>
          <StyleProvider cache={createCache()}>
            <HydratedRouter />
          </StyleProvider>
        </I18nextProvider>
      </StrictMode>
    );
  });
}

main().catch(error => console.error(error));
