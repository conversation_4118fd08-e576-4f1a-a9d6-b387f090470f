import { type RouteConfig, index, layout, prefix, route } from '@react-router/dev/routes';

export default [
  layout('./components/layouts/MainLayout.tsx', [
    index('./modules/home/<USER>'),
    ...prefix('/system-settings/menu', [
      route('/categories', './modules/platform/menu/MenuCategories.tsx'),
      route('/items', './modules/platform/menu/MenuItems.tsx'),
    ]),
    route('*', './modules/pages/NotFound.tsx'),
  ]),
  route('/locales/:lng/:ns', './libs/locales.ts'),
] satisfies RouteConfig;
