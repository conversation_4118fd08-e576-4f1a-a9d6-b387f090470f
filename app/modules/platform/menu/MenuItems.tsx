import React, { useEffect, useState, useRef } from 'react';
import { Page<PERSON>ontainer, ProCard, EditableProTable } from '@ant-design/pro-components';
import { Button, Popconfirm, message, Space, App, Switch, Tooltip, Select, Modal, Input, Row, Col } from 'antd';
import type { FormInstance } from 'antd';
import * as Icons from '@ant-design/icons';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  QuestionCircleOutlined,
  UserOutlined,
  HomeOutlined,
  SettingOutlined,
  AppstoreOutlined,
  FileOutlined,
  TeamOutlined,
  ShopOutlined,
  DashboardOutlined,
  BarsOutlined,
  SearchOutlined,
  BellOutlined,
  LockOutlined,
  UnlockOutlined,
  KeyOutlined,
  MailOutlined,
  <PERSON>Outlined,
  PayCircleOutlined,
  VideoCameraOutlined,
  CameraOutlined,
  PictureOutlined,
  ClockCircleOutlined,
  CalendarOutlined,
  PrinterOutlined,
  PhoneOutlined,
  MobileOutlined,
  TabletOutlined,
  <PERSON>Outlined,
  <PERSON>Outlined,
  <PERSON>Outlined,
  Gith<PERSON>Outlined,
  <PERSON>uOutlined,
  <PERSON>uFoldOutlined,
  MenuUnfoldOutlined,
  Plus<PERSON><PERSON>cleOutlined,
  SubnodeOutlined,
} from '@ant-design/icons';
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { useTranslation } from 'react-i18next';
import { getMenuItems, createMenuItem, updateMenuItem, deleteMenuItem } from '~/service/platform/menu.service';

class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <h2>Something went wrong.</h2>
          <Button
            type='primary'
            onClick={() => this.setState({ hasError: false })}
          >
            Try again
          </Button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Create a custom interface for the menu items with the fields we need
interface EditableMenuItem {
  id: string;
  name: string;
  nameZh: string;
  path: string;
  icon: string;
  description?: string;
  parentId?: string;
  seq: number;
  valid: boolean;
  children?: EditableMenuItem[];
}

// Common icons to choose from
const COMMON_ICONS = [
  'UserOutlined',
  'HomeOutlined',
  'SettingOutlined',
  'AppstoreOutlined',
  'FileOutlined',
  'TeamOutlined',
  'ShopOutlined',
  'DashboardOutlined',
  'BarsOutlined',
  'SearchOutlined',
  'BellOutlined',
  'LockOutlined',
  'UnlockOutlined',
  'KeyOutlined',
  'MailOutlined',
  'CloudOutlined',
  'PayCircleOutlined',
  'VideoCameraOutlined',
  'CameraOutlined',
  'PictureOutlined',
  'ClockCircleOutlined',
  'CalendarOutlined',
  'PrinterOutlined',
  'PhoneOutlined',
  'MobileOutlined',
  'TabletOutlined',
  'WindowsOutlined',
  'AppleOutlined',
  'AndroidOutlined',
  'GithubOutlined',
  'MenuOutlined',
  'MenuFoldOutlined',
  'MenuUnfoldOutlined',
];

// Icon Selector component
const IconSelector = ({ value, onChange }: { value?: string; onChange?: (value: string) => void }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedIcon, setSelectedIcon] = useState(value || '');

  const filteredIcons = searchText
    ? COMMON_ICONS.filter(icon => icon.toLowerCase().includes(searchText.toLowerCase()))
    : COMMON_ICONS;

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    if (onChange) {
      onChange(selectedIcon);
    }
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleIconClick = (iconName: string) => {
    setSelectedIcon(iconName);
  };

  return (
    <>
      <Space>
        <Button onClick={showModal}>
          {value ? (
            <>
              <IconRenderer name={value} />
              <span style={{ marginLeft: 8 }}>Change Icon</span>
            </>
          ) : (
            'Select Icon'
          )}
        </Button>
        {value && (
          <Button
            danger
            onClick={() => onChange?.('')}
            icon={<DeleteOutlined />}
          >
            Clear
          </Button>
        )}
      </Space>

      <Modal
        title='Select Icon'
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={700}
      >
        <Input
          placeholder='Search icons...'
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          style={{ marginBottom: 16 }}
        />

        <Row
          gutter={[16, 16]}
          style={{ maxHeight: '400px', overflow: 'auto' }}
        >
          {filteredIcons.map(iconName => {
            // @ts-ignore - we're dynamically accessing icons
            const Icon = Icons[iconName];

            return (
              <Col
                span={6}
                key={iconName}
              >
                <div
                  style={{
                    padding: '12px 8px',
                    border: `1px solid ${selectedIcon === iconName ? '#1890ff' : '#d9d9d9'}`,
                    borderRadius: '4px',
                    cursor: 'pointer',
                    background: selectedIcon === iconName ? '#e6f7ff' : 'transparent',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexDirection: 'column',
                  }}
                  onClick={() => handleIconClick(iconName)}
                >
                  {Icon && <Icon style={{ fontSize: 24, marginBottom: 8 }} />}
                  <div style={{ fontSize: 12, textAlign: 'center' }}>{iconName}</div>
                </div>
              </Col>
            );
          })}
        </Row>
      </Modal>
    </>
  );
};

// Dynamic icon rendering function
const IconRenderer = ({ name }: { name: string }) => {
  // @ts-ignore - we're dynamically accessing icons
  const Icon = Icons[name];

  if (Icon) {
    return (
      <Tooltip title={name}>
        <span style={{ fontSize: '16px', display: 'inline-flex', alignItems: 'center' }}>
          <Icon /> <span style={{ marginLeft: 8, fontSize: '14px', opacity: 0.7 }}>{name}</span>
        </span>
      </Tooltip>
    );
  }

  return (
    <Tooltip title={`Icon "${name}" not found`}>
      <span>
        <QuestionCircleOutlined /> <span style={{ marginLeft: 8, fontSize: '14px', opacity: 0.7 }}>{name}</span>
      </span>
    </Tooltip>
  );
};

const MenuItemsContent: React.FC = () => {
  const [items, setItems] = useState<EditableMenuItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const actionRef = useRef<ActionType>(null);
  const formRef = useRef<ProFormInstance>(undefined);
  const { message } = App.useApp();
  const { i18n } = useTranslation();
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);

  // Transform flat array to tree structure
  const buildTreeData = (data: EditableMenuItem[]): EditableMenuItem[] => {
    const map: Record<string, number> = {};
    const treeData: EditableMenuItem[] = [];
    const sorted = [...data].sort((a, b) => a.seq - b.seq);

    // Create a map of id to index
    sorted.forEach((item, index) => {
      map[item.id] = index;
      item.children = [];
    });

    // Build the tree
    sorted.forEach(item => {
      // If has parentId and parent exists in our data
      if (item.parentId && map[item.parentId] !== undefined) {
        const parent = sorted[map[item.parentId]];
        if (parent.children) {
          parent.children.push({
            ...item,
            children: item.children && item.children.length > 0 ? item.children : undefined,
          });
        }
      } else {
        // Add root level items
        treeData.push({ ...item, children: item.children && item.children.length > 0 ? item.children : undefined });
      }
    });

    // Clean up empty children arrays
    const cleanupEmptyChildren = (items: EditableMenuItem[]): EditableMenuItem[] => {
      return items.map(item => {
        if (item.children && item.children.length === 0) {
          const { children, ...rest } = item;
          return rest;
        }
        if (item.children && item.children.length > 0) {
          return {
            ...item,
            children: cleanupEmptyChildren(item.children),
          };
        }
        return item;
      });
    };

    return cleanupEmptyChildren(treeData);
  };

  const fetchItems = async () => {
    try {
      setLoading(true);
      const response = await getMenuItems();
      // Transform to tree structure
      const treeData = buildTreeData(response.data);
      setItems(treeData);
    } catch (error) {
      message.error('Failed to fetch menu items');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchItems();
  }, []);

  const handleDelete = async (id: string) => {
    // For new unsaved records, just remove from state
    if (id.startsWith('new-')) {
      setItems(prevItems => {
        const removeItemFromTree = (items: EditableMenuItem[]): EditableMenuItem[] => {
          return items
            .filter(item => item.id !== id)
            .map(item => {
              if (item.children) {
                return {
                  ...item,
                  children: removeItemFromTree(item.children),
                };
              }
              return item;
            });
        };

        return removeItemFromTree(prevItems);
      });

      message.success('Item removed');
      return;
    }

    // For saved records, delete from database
    try {
      await deleteMenuItem(id);
      message.success('Item deleted successfully');
      fetchItems();
    } catch (error) {
      message.error('Failed to delete item');
      console.error(error);
    }
  };

  const handleSave = async (row: EditableMenuItem) => {
    const { id } = row;
    const isNewRecord = id.startsWith('new-');

    try {
      // Remove children property if it exists before sending to API
      const { children, ...rowToSave } = row;

      if (isNewRecord) {
        // Create new record
        await createMenuItem(rowToSave);
        message.success('Item created successfully');
        // For new records, we need to refresh the list to get the server-generated ID
        fetchItems();
      } else {
        // Find the original item to check if seq or parentId has changed
        const flatItems = flattenTree(items);
        const originalItem = flatItems.find(item => item.id === id);
        const hasSeqChanged = originalItem && originalItem.seq !== row.seq;
        const hasParentChanged = originalItem && originalItem.parentId !== row.parentId;

        // Update existing record
        await updateMenuItem(id, rowToSave);
        message.success('Item updated successfully');

        if (hasSeqChanged || hasParentChanged) {
          // If sequence or parent changed, refetch and rebuild tree
          fetchItems();
        } else {
          // If sequence didn't change, update state directly to preserve order
          const updateNodeInTree = (nodes: EditableMenuItem[], updatedNode: EditableMenuItem): EditableMenuItem[] => {
            return nodes.map(node => {
              if (node.id === updatedNode.id) {
                return { ...updatedNode, children: node.children };
              }
              if (node.children) {
                return {
                  ...node,
                  children: updateNodeInTree(node.children, updatedNode),
                };
              }
              return node;
            });
          };

          setItems(prevItems => updateNodeInTree(prevItems, { ...row }));
        }
      }

      return true;
    } catch (error) {
      message.error('Failed to save item');
      console.error(error);
      return false;
    }
  };

  // Helper function to flatten tree for editing purposes
  const flattenTree = (treeData: EditableMenuItem[]): EditableMenuItem[] => {
    let flatData: EditableMenuItem[] = [];

    treeData.forEach(item => {
      const { children, ...flatItem } = item;
      flatData.push(flatItem);

      if (children && children.length > 0) {
        flatData = [...flatData, ...flattenTree(children)];
      }
    });

    return flatData;
  };

  const handleStatusChange = async (checked: boolean, record: EditableMenuItem) => {
    try {
      const updatedItem = { ...record, valid: checked };
      await updateMenuItem(record.id, updatedItem);

      // Update state directly instead of refetching all items
      setItems(prevItems => prevItems.map(item => (item.id === record.id ? { ...item, valid: checked } : item)));

      message.success('Status updated successfully');
    } catch (error) {
      message.error('Failed to update status');
      console.error(error);
    }
  };

  // Add parentId selection from existing items
  const parentOptions = flattenTree(items).map(item => ({
    label: item.name,
    value: item.id,
  }));

  const addItem = (parentId?: string, siblingId?: string) => {
    let newSeq = 0;
    let newParentId = parentId || '';

    // Create new item with a unique ID
    const newId = `new-${Date.now()}`;

    // If adding a sibling, use the same parent and calculate appropriate seq
    if (siblingId) {
      const flatItems = flattenTree(items);
      const sibling = flatItems.find(item => item.id === siblingId);

      if (sibling) {
        newParentId = sibling.parentId || '';

        // Set seq to be right after the current sibling
        newSeq = sibling.seq + 1;

        // Log for debugging
        console.log('Adding sibling after:', sibling.id, 'with seq:', newSeq);
      }
    }
    // If adding a child, set parent and set seq to be the last child
    else if (parentId) {
      const flatItems = flattenTree(items);
      const parent = flatItems.find(item => item.id === parentId);

      if (parent) {
        if (parent.children && parent.children.length > 0) {
          // If parent has children, put new item at the end
          newSeq = parent.children.length;
        }

        // Expand the parent row
        setExpandedKeys(prev => [...prev, parentId]);

        // Log for debugging
        console.log('Adding child to:', parentId, 'with seq:', newSeq);
      }
    }
    // If adding a root item, put at the end
    else {
      const rootItems = items;
      newSeq = rootItems.length;

      // Log for debugging
      console.log('Adding root item with seq:', newSeq);
    }

    // Create new item
    const newItem: EditableMenuItem = {
      id: newId,
      name: '',
      nameZh: '',
      path: '',
      icon: '',
      description: '',
      parentId: newParentId,
      seq: newSeq,
      valid: true,
    };

    console.log('New item created:', newItem);

    // Add to local state based on the operation type
    if (parentId) {
      // Adding as a child
      setItems(prevItems => {
        console.log('Adding as child to parent:', parentId);
        return addChildToItem(prevItems, parentId, newItem);
      });
    } else if (siblingId) {
      // Adding as a sibling immediately after the current item
      setItems(prevItems => {
        console.log('Adding as sibling after:', siblingId);
        const result = addSiblingAfterItem(prevItems, siblingId, newItem);
        console.log('Result after adding sibling:', result);
        return result;
      });
    } else {
      // Adding as a root item at the end
      setItems(prevItems => {
        console.log('Adding as root item');
        return [...prevItems, newItem];
      });
    }

    // Start editing the new item
    setEditableKeys([newId]);
  };

  // Helper function to add a sibling immediately after the referenced item
  const addSiblingAfterItem = (
    items: EditableMenuItem[],
    siblingId: string,
    newItem: EditableMenuItem
  ): EditableMenuItem[] => {
    console.log('addSiblingAfterItem called with items:', items.length, 'siblingId:', siblingId);

    // Special handling for first row if it matches the siblingId
    if (items.length > 0 && items[0].id === siblingId) {
      console.log('First row sibling match found');
      return [items[0], newItem, ...items.slice(1)];
    }

    const result: EditableMenuItem[] = [];
    let siblingFound = false;

    // Handle for all items
    for (let i = 0; i < items.length; i++) {
      // Clone the current item to avoid mutations
      const currentItem = { ...items[i] };

      // Push the current item to results
      result.push(currentItem);

      // If this is the sibling we're targeting, add the new item right after it at this level
      if (currentItem.id === siblingId) {
        console.log('Sibling match found at index:', i);
        result.push(newItem);
        siblingFound = true;
        continue; // Skip processing children since we found the sibling at this level
      }

      // If this item has children, recursively process them
      if (currentItem.children && currentItem.children.length > 0) {
        const processedChildren = addSiblingAfterItem(currentItem.children, siblingId, newItem);

        // Check if a sibling was found in the children
        if (processedChildren.length > currentItem.children.length) {
          currentItem.children = processedChildren;
          siblingFound = true;
        } else {
          currentItem.children = processedChildren;
        }
      }
    }

    console.log('Result length:', result.length, 'Original length:', items.length);
    return result;
  };

  // Helper function to add a child to a nested item in the tree
  const addChildToItem = (
    items: EditableMenuItem[],
    parentId: string,
    newChild: EditableMenuItem
  ): EditableMenuItem[] => {
    return items.map(item => {
      if (item.id === parentId) {
        return {
          ...item,
          children: item.children ? [...item.children, newChild] : [newChild],
        };
      }
      if (item.children) {
        return {
          ...item,
          children: addChildToItem(item.children, parentId, newChild),
        };
      }
      return item;
    });
  };

  // Updated columns to include parentId selection
  const columns: ProColumns<EditableMenuItem>[] = [
    {
      title: 'Name',
      dataIndex: 'name',
      formItemProps: {
        rules: [{ required: true, message: 'Please input the item name!' }],
      },
    },
    {
      title: '名字',
      dataIndex: 'nameZh',
      formItemProps: {
        rules: [{ required: true, message: 'Please input the Chinese name!' }],
      },
    },
    {
      title: 'Path',
      dataIndex: 'path',
      formItemProps: {
        rules: [{ required: true, message: 'Please input the path!' }],
      },
    },
    {
      title: 'Icon',
      dataIndex: 'icon',
      formItemProps: {
        rules: [{ required: true, message: 'Please input the icon!' }],
      },
      render: (_, record) => <IconRenderer name={record.icon} />,
      renderFormItem: (_, { type, defaultRender, ...rest }, form) => {
        if (type === 'form') {
          return (
            <IconSelector
              value={form.getFieldValue('icon')}
              onChange={value => form.setFieldValue('icon', value)}
            />
          );
        }
        return defaultRender(_);
      },
    },
    {
      title: 'Description',
      dataIndex: 'description',
      valueType: 'textarea',
    },
    {
      title: 'Parent ID',
      dataIndex: 'parentId',
      renderFormItem: (_, { type, defaultRender, ...rest }, form) => {
        if (type === 'form') {
          const currentId = form.getFieldValue('id');
          // Filter out self from options to prevent circular reference
          const filteredOptions = parentOptions.filter(option => option.value !== currentId);

          return (
            <Select
              allowClear
              placeholder='Select a parent item'
              options={filteredOptions}
              style={{ width: '100%' }}
            />
          );
        }
        return defaultRender(_);
      },
    },
    {
      title: 'Sequence',
      dataIndex: 'seq',
      valueType: 'digit',
      formItemProps: {
        rules: [{ required: true, message: 'Please input the sequence!' }],
      },
      sorter: (a, b) => a.seq - b.seq,
      defaultSortOrder: 'ascend',
    },
    {
      title: 'Status',
      dataIndex: 'valid',
      valueType: 'switch',
      render: (_, record) => (
        <Switch
          checked={record.valid}
          disabled={editableKeys.length > 0 && !editableKeys.includes(record.id)}
          onChange={checked => handleStatusChange(checked, record)}
        />
      ),
    },
    {
      title: 'Action',
      valueType: 'option',
      render: (text, record, _, action) => [
        <Button
          key='edit'
          type='primary'
          icon={<EditOutlined />}
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
          disabled={editableKeys.length > 0}
          style={{ marginRight: 8 }}
        />,
        <Button
          key='addSibling'
          type='default'
          icon={<SubnodeOutlined />}
          onClick={() => addItem(undefined, record.id)}
          disabled={editableKeys.length > 0}
          style={{ marginRight: 8 }}
          title='Add Sibling'
        />,
        <Button
          key='addChild'
          type='default'
          icon={<PlusCircleOutlined />}
          onClick={() => addItem(record.id)}
          disabled={editableKeys.length > 0}
          style={{ marginRight: 8 }}
          title='Add Child'
        />,
        <Popconfirm
          key='delete'
          title='Are you sure you want to delete this item?'
          onConfirm={() => handleDelete(record.id)}
          okText='Yes'
          cancelText='No'
          disabled={editableKeys.length > 0}
        >
          <Button
            type='primary'
            danger
            icon={<DeleteOutlined />}
            disabled={editableKeys.length > 0}
          />
        </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer
      header={{
        title: 'Menu Items',
        subTitle: 'Manage your menu items',
      }}
    >
      <ProCard
        title='Menu Items'
        extra={
          <Button
            type='primary'
            icon={<PlusOutlined />}
            onClick={() => addItem()}
            disabled={editableKeys.length > 0}
          >
            Add Root Item
          </Button>
        }
      >
        <EditableProTable<EditableMenuItem>
          rowKey='id'
          actionRef={actionRef}
          formRef={formRef}
          columns={columns}
          value={items}
          loading={loading}
          expandable={{
            defaultExpandAllRows: true,
            expandedRowKeys: expandedKeys,
            onExpand: (expanded, record) => {
              if (expanded) {
                setExpandedKeys(keys => [...keys, record.id]);
              } else {
                setExpandedKeys(keys => keys.filter(key => key !== record.id));
              }
            },
          }}
          editable={{
            type: 'multiple',
            editableKeys,
            onSave: async (key, row) => {
              await handleSave(row);
            },
            onChange: setEditableKeys,
            onCancel: async key => {
              // If canceling a new record, remove it
              if (String(key).startsWith('new-')) {
                handleDelete(String(key));
              }
              return Promise.resolve();
            },
            actionRender: (row, config, defaultDoms) => {
              return [defaultDoms.save, defaultDoms.cancel];
            },
          }}
          pagination={false}
          search={false}
          childrenColumnName='children'
          request={async (params, sort, filter) => {
            return {
              data: items,
              success: true,
              total: items.length,
            };
          }}
          options={{
            density: false,
            fullScreen: true,
            reload: () => fetchItems(),
            setting: false,
          }}
        />
      </ProCard>
    </PageContainer>
  );
};

const MenuItems: React.FC = () => {
  return (
    <App>
      <ErrorBoundary>
        <MenuItemsContent />
      </ErrorBoundary>
    </App>
  );
};

export default MenuItems;
