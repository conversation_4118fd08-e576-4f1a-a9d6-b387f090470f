import React, { useEffect, useState, useRef } from 'react';
import { <PERSON><PERSON>ontainer, ProCard, ProTable } from '@ant-design/pro-components';
import { Button, Popconfirm, App, Form, Input, InputNumber, Switch, Space, ConfigProvider } from 'antd';
import { DeleteOutlined, EditOutlined, SaveOutlined, CloseOutlined, PlusOutlined } from '@ant-design/icons';
import type { SortOrder } from 'antd/es/table/interface';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { useTranslation } from 'react-i18next';

import {
  getMenuCategories,
  createMenuCategory,
  updateMenuCategory,
  deleteMenuCategory,
} from '~/service/platform/menu.service';

class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <h2>Something went wrong.</h2>
          <Button
            type='primary'
            onClick={() => this.setState({ hasError: false })}
          >
            Try again
          </Button>
        </div>
      );
    }

    return this.props.children;
  }
}

interface EditableCellProps extends React.HTMLAttributes<HTMLElement> {
  editing: boolean;
  dataIndex: string;
  title: string;
  inputType: 'text' | 'number' | 'switch';
  record: any;
  index: number;
  children: React.ReactNode;
}

const EditableCell: React.FC<EditableCellProps> = ({
  editing,
  dataIndex,
  title,
  inputType,
  record,
  index,
  children,
  ...restProps
}) => {
  const inputNode = inputType === 'number' ? <InputNumber min={0} /> : inputType === 'switch' ? <Switch /> : <Input />;

  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item
          name={dataIndex}
          style={{ margin: 0 }}
          rules={[
            {
              required: ['name', 'nameZh'].includes(dataIndex),
              message: `Please Input ${title}!`,
            },
          ]}
          valuePropName={inputType === 'switch' ? 'checked' : 'value'}
        >
          {inputNode}
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};

const MenuCategoriesContent: React.FC = () => {
  const [form] = Form.useForm();
  // Use any to overcome TypeScript limitations temporarily
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingKey, setEditingKey] = useState<string>('');
  const actionRef = useRef<ActionType | undefined>(undefined);
  const { message } = App.useApp();
  const { i18n } = useTranslation();

  const isEditing = (record: any) => String(record.id) === editingKey;

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await getMenuCategories();
      const sortedCategories = response.data.sort((a, b) => (a.seq || 0) - (b.seq || 0));
      setCategories(sortedCategories);
    } catch (error) {
      message.error('Failed to fetch menu categories');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  const edit = (record: any) => {
    form.setFieldsValue({
      name: record.name,
      nameZh: record.nameZh,
      description: record.description,
      seq: record.seq,
      valid: record.valid,
    });
    setEditingKey(String(record.id));
  };

  const cancel = () => {
    setEditingKey('');

    // If adding a new record, remove it from the list
    if (editingKey.startsWith('new-')) {
      setCategories(categories.filter(item => String(item.id) !== editingKey));
    }
  };

  const save = async (id: string) => {
    try {
      const row = await form.validateFields();
      const isNewRecord = id.startsWith('new-');

      if (isNewRecord) {
        // Create new record
        await createMenuCategory(row);
        message.success('Category created successfully');
      } else {
        // Update existing record
        await updateMenuCategory(id, row);
        message.success('Category updated successfully');
      }

      setEditingKey('');
      fetchCategories();
    } catch (error) {
      message.error('Failed to save category');
      console.error(error);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteMenuCategory(id);
      message.success('Category deleted successfully');
      fetchCategories();
    } catch (error) {
      message.error('Failed to delete category');
      console.error(error);
    }
  };

  const addNew = () => {
    const newId = `new-${Date.now()}`;
    const newItem = {
      id: newId,
      name: '',
      nameZh: '',
      description: '',
      seq: categories.length,
      valid: true,
    };

    setCategories([...categories, newItem]);
    edit(newItem);
  };

  const columns: ProColumns<any>[] = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      onCell: record => ({
        record,
        dataIndex: 'name',
        title: 'Name',
        editing: isEditing(record),
        inputType: 'text',
      }),
    },
    {
      title: '名字',
      dataIndex: 'nameZh',
      key: 'nameZh',
      onCell: record => ({
        record,
        dataIndex: 'nameZh',
        title: '名字',
        editing: isEditing(record),
        inputType: 'text',
      }),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      onCell: record => ({
        record,
        dataIndex: 'description',
        title: 'Description',
        editing: isEditing(record),
        inputType: 'text',
      }),
    },
    {
      title: 'Order',
      dataIndex: 'seq',
      key: 'seq',
      sorter: (a, b) => (a.seq || 0) - (b.seq || 0),
      defaultSortOrder: 'ascend' as SortOrder,
      onCell: record => ({
        record,
        dataIndex: 'seq',
        title: 'Order',
        editing: isEditing(record),
        inputType: 'number',
      }),
    },
    {
      title: 'Status',
      dataIndex: 'valid',
      key: 'valid',
      render: valid => (
        <Switch
          checked={!!valid}
          disabled
        />
      ),
      onCell: record => ({
        record,
        dataIndex: 'valid',
        title: 'Status',
        editing: isEditing(record),
        inputType: 'switch',
      }),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => {
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button
              type='primary'
              icon={<SaveOutlined />}
              onClick={() => save(String(record.id))}
            />
            <Button
              type='default'
              icon={<CloseOutlined />}
              onClick={cancel}
            />
          </Space>
        ) : (
          <Space>
            <Button
              type='primary'
              icon={<EditOutlined />}
              disabled={editingKey !== ''}
              onClick={() => edit(record)}
            />
            <Popconfirm
              title='Are you sure you want to delete this category?'
              onConfirm={() => handleDelete(String(record.id))}
              okText='Yes'
              cancelText='No'
              disabled={editingKey !== ''}
            >
              <Button
                type='primary'
                danger
                icon={<DeleteOutlined />}
                disabled={editingKey !== ''}
              />
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  const components = {
    body: {
      cell: EditableCell,
    },
  };

  return (
    <PageContainer
      header={{
        title: 'Menu Categories',
        subTitle: 'Manage your menu categories',
      }}
    >
      <ProCard>
        <Form
          form={form}
          component='div'
        >
          <ProTable<any>
            rowKey='id'
            actionRef={actionRef}
            components={components}
            dataSource={categories}
            columns={columns}
            loading={loading}
            toolBarRender={() => [
              <Button
                key='add'
                type='primary'
                icon={<PlusOutlined />}
                onClick={addNew}
                disabled={editingKey !== ''}
              >
                Add Category
              </Button>,
            ]}
            pagination={{
              defaultPageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
            }}
            search={false}
            options={false}
          />
        </Form>
      </ProCard>
    </PageContainer>
  );
};

const MenuCategories: React.FC = () => {
  return (
    <ErrorBoundary>
      <MenuCategoriesContent />
    </ErrorBoundary>
  );
};

export default MenuCategories;
